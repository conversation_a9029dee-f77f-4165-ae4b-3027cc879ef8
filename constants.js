/* ------------------------------------------------------------------
 *  constants.js   ── pure values only (no functions)
 * ------------------------------------------------------------------*/

/*──────────────────────────────
  Supported file extensions
──────────────────────────────*/
export const SUPPORTED_AUDIO_EXTENSIONS = ['.mp3', '.wav', '.ogg', '.flac', '.aac'];
export const SUPPORTED_DOC_EXTENSIONS   = ['.txt', '.docx'];

/*──────────────────────────────
  UI helper data
──────────────────────────────*/
export const BREAK_DURATIONS = [
  { label: '0.5s Break', value: 0.5 },
  { label: '1.0s Break', value: 1.0 },
  { label: '2.0s Break', value: 2.0 },
];

/*===================================================================
  GOOGLE CLOUD TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_G_TTS_VOICE_NAME  = 'en-GB-News-L';
export const G_TTS_SPEECH_SPEED        = 0.75;
export const G_TTS_SAMPLE_RATE_HERTZ   = 44_100;

/* Multi-platform env-var lookup for the Google Cloud TTS API key */
export const G_TTS_API_KEY =
  /* Node / server-side */
  (typeof process !== 'undefined' && process.env && (
    process.env.G_TTS_API_KEY ||
    process.env.API_KEY ||                           // legacy
    process.env.NEXT_PUBLIC_G_TTS_API_KEY            // Next.js client bundle
  )) ||
  /* Vite / import.meta style */
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_G_TTS_API_KEY) ||
  null;

// Debug logging for Google TTS API key
console.log('Constants: import.meta available:', typeof import.meta !== 'undefined');
console.log('Constants: import.meta.env available:', typeof import.meta !== 'undefined' && !!import.meta.env);
if (typeof import.meta !== 'undefined' && import.meta.env) {
  console.log('Constants: All env vars:', Object.keys(import.meta.env));
  console.log('Constants: VITE_G_TTS_API_KEY from env length:', import.meta.env.VITE_G_TTS_API_KEY?.length || 0);
  console.log('Constants: VITE_G_TTS_API_KEY starts with AIza:', import.meta.env.VITE_G_TTS_API_KEY?.startsWith('AIza') || false);
}
console.log('Constants: G_TTS_API_KEY final length:', G_TTS_API_KEY?.length || 0);
console.log('Constants: G_TTS_API_KEY starts with AIza:', G_TTS_API_KEY?.startsWith('AIza') || false);



/* Extension → Google audioEncoding map */
export const EXTENSION_TO_ENCODING_GOOGLE = {
  '.wav':  'LINEAR16',
  '.mp3':  'MP3',
  '.ogg':  'OGG_OPUS',
  '.flac': 'FLAC',
  '.aac':  'MP3',   // synth as MP3 for widest support
};

/*===================================================================
  MICROSOFT AZURE TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_MS_TTS_VOICE_NAME = 'en-US-AvaMultilingualNeural';
export const MS_TTS_SPEECH_SPEED       = 'default';        // 'slow'|'medium'|'fast' or %
export const MS_TTS_SAMPLE_RATE_HERTZ  = 'audio-24khz-160kbitrate-mono-mp3';

/* Env-var lookup that works for Node, Next.js (NEXT_PUBLIC_), and Vite (VITE_) */
export const MS_TTS_API_KEY =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_API_KEY ||
    process.env.NEXT_PUBLIC_MS_TTS_API_KEY
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_API_KEY) ||
  null;

export const MS_TTS_SERVICE_REGION =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_SERVICE_REGION ||
    process.env.NEXT_PUBLIC_MS_TTS_SERVICE_REGION
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_SERVICE_REGION) ||
  null;

/*===================================================================
  GOOGLE GEMINI AI CONFIGURATION
===================================================================*/
// Available Gemini models (latest 2.5 family)
export const GEMINI_MODELS = {
    'gemini-2.5-flash': {
        name: 'Gemini 2.5 Flash ⚡',
        description: 'Latest fast model with adaptive thinking - best price-performance',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent'
    },
    'gemini-2.5-pro': {
        name: 'Gemini 2.5 Pro 🧠',
        description: 'Most powerful thinking model with maximum accuracy',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent'
    },
    'gemini-2.0-flash': {
        name: 'Gemini 2.0 Flash',
        description: 'Next-gen features with superior speed',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'
    },
    'gemini-1.5-flash': {
        name: 'Gemini 1.5 Flash (Legacy)',
        description: 'Previous generation fast model',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    },
    'gemini-1.5-pro': {
        name: 'Gemini 1.5 Pro (Legacy)',
        description: 'Previous generation capable model',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent'
    }
};

// Default model - using the latest and best price-performance
export const DEFAULT_GEMINI_MODEL = 'gemini-2.5-flash';
export const GEMINI_API_BASE_URL = GEMINI_MODELS[DEFAULT_GEMINI_MODEL].url;

/* Env-var lookup for Google Gemini API key */
export const GEMINI_API_KEY =
  (typeof process !== 'undefined' && process.env && (
    process.env.GEMINI_API_KEY ||
    process.env.NEXT_PUBLIC_GEMINI_API_KEY
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_GEMINI_API_KEY) ||
  null;

// Debug logging for Gemini API key
console.log('Constants: GEMINI_API_KEY final length:', GEMINI_API_KEY?.length || 0);
console.log('Constants: GEMINI_API_KEY starts with AIza:', GEMINI_API_KEY?.startsWith('AIza') || false);

/*===================================================================
  LOCAL WHISPER CONFIGURATION
===================================================================*/
// Available local Whisper models (ordered by size/quality)
export const WHISPER_MODELS = [
  { value: 'tiny', name: 'tiny (39 MB, fastest, lowest quality)' },
  { value: 'base', name: 'base (74 MB, fast, good quality)' },
  { value: 'small', name: 'small (244 MB, balanced)' },
  { value: 'medium', name: 'medium (769 MB, good quality)' },
  { value: 'large', name: 'large (1550 MB, best quality)' },
  { value: 'large-v2', name: 'large-v2 (1550 MB, improved large)' },
  { value: 'large-v3', name: 'large-v3 (1550 MB, latest large)' }
];

// Default model for new users (good balance of speed and quality)
export const DEFAULT_WHISPER_MODEL = 'base';

// Whisper processing endpoint (local Python server)
export const WHISPER_ENDPOINT = 'http://localhost:8001/transcribe';



/*===================================================================
  AI VOICE CREATOR ENCODINGS - Updated: 2025-06-07 13:19
===================================================================*/
export const AI_VOICE_CREATOR_ENCODINGS = {
  'mp3': {
    extension: '.mp3',
    apiValue: 'MP3',
    mimeType: 'audio/mpeg'
  },
  'wav': {
    extension: '.wav',
    apiValue: 'LINEAR16',
    mimeType: 'audio/wav'
  },
  'ogg': {
    extension: '.ogg',
    apiValue: 'OGG_OPUS',
    mimeType: 'audio/ogg'
  },
  'flac': {
    extension: '.flac',
    apiValue: 'FLAC',
    mimeType: 'audio/flac'
  }
};
